'use client';

import { useState, useMemo } from 'react';
import { useFileStore } from '../../../../stores/fileStore';
import FileIcon from './components/fileIcon';
import { ChevronRight, ChevronDown, Plus, FolderPlus } from 'lucide-react';

interface FileExplorerProps {
  onFileSelect: (path: string) => void;
}

interface FileNode {
  name: string;
  path: string;
  isFolder: boolean;
  children?: FileNode[];
}

export function FileExplorer({ onFileSelect }: FileExplorerProps) {
  const { files, addFile, createFolder } = useFileStore();
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['']));
  const [showCreateMenu, setShowCreateMenu] = useState<string | null>(null);

  const fileTree = useMemo(() => {
    const tree: FileNode[] = [];
    const pathMap = new Map<string, FileNode>();

    // Add root folder
    const rootNode: FileNode = {
      name: 'Project',
      path: '',
      isFolder: true,
      children: []
    };
    tree.push(rootNode);
    pathMap.set('', rootNode);

    // Process all file paths
    Object.keys(files).forEach(filePath => {
      const parts = filePath.split('/').filter(Boolean);
      let currentPath = '';

      parts.forEach((part, index) => {
        const parentPath = currentPath;
        currentPath = currentPath ? `${currentPath}/${part}` : part;
        const isLastPart = index === parts.length - 1;

        if (!pathMap.has(currentPath)) {
          const node: FileNode = {
            name: part,
            path: currentPath,
            isFolder: !isLastPart,
            children: !isLastPart ? [] : undefined
          };

          pathMap.set(currentPath, node);

          // Add to parent
          const parent = pathMap.get(parentPath);
          if (parent && parent.children) {
            parent.children.push(node);
          }
        }
      });
    });

    // Sort children: folders first, then files, both alphabetically
    const sortChildren = (node: FileNode) => {
      if (node.children) {
        node.children.sort((a, b) => {
          if (a.isFolder && !b.isFolder) return -1;
          if (!a.isFolder && b.isFolder) return 1;
          return a.name.localeCompare(b.name);
        });
        node.children.forEach(sortChildren);
      }
    };

    tree.forEach(sortChildren);
    return tree;
  }, [files]);

  const toggleFolder = (path: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedFolders(newExpanded);
  };

  const handleCreateFile = async (parentPath: string) => {
    const fileName = prompt('Enter file name:');
    if (fileName) {
      const fullPath = parentPath ? `${parentPath}/${fileName}` : fileName;
      await addFile(fullPath, '');
      onFileSelect(fullPath);
    }
    setShowCreateMenu(null);
  };

  const handleCreateFolder = async (parentPath: string) => {
    const folderName = prompt('Enter folder name:');
    if (folderName) {
      const fullPath = parentPath ? `${parentPath}/${folderName}` : folderName;
      await createFolder(fullPath);
      setExpandedFolders(prev => new Set([...prev, fullPath]));
    }
    setShowCreateMenu(null);
  };

  const renderNode = (node: FileNode, depth = 0) => {
    const isExpanded = expandedFolders.has(node.path);
    const hasChildren = node.children && node.children.length > 0;

    return (
      <div key={node.path}>
        <div
          className="flex items-center gap-1 px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer group"
          style={{ paddingLeft: `${depth * 12 + 8}px` }}
          onClick={() => {
            if (node.isFolder) {
              toggleFolder(node.path);
            } else {
              onFileSelect(node.path);
            }
          }}
          onContextMenu={(e) => {
            e.preventDefault();
            if (node.isFolder) {
              setShowCreateMenu(showCreateMenu === node.path ? null : node.path);
            }
          }}
        >
          {node.isFolder && (
            <button className="p-0.5 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
              {isExpanded ? (
                <ChevronDown size={14} />
              ) : (
                <ChevronRight size={14} />
              )}
            </button>
          )}
          
          <FileIcon 
            fileName={node.name} 
            isFolder={node.isFolder} 
            isOpen={isExpanded} 
          />
          
          <span className="text-sm flex-1 truncate">
            {node.name}
          </span>

          {node.isFolder && (
            <div className="opacity-0 group-hover:opacity-100 flex gap-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleCreateFile(node.path);
                }}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="New File"
              >
                <Plus size={12} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleCreateFolder(node.path);
                }}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="New Folder"
              >
                <FolderPlus size={12} />
              </button>
            </div>
          )}
        </div>

        {showCreateMenu === node.path && (
          <div className="absolute bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded shadow-lg z-10 py-1">
            <button
              onClick={() => handleCreateFile(node.path)}
              className="w-full px-3 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              New File
            </button>
            <button
              onClick={() => handleCreateFolder(node.path)}
              className="w-full px-3 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              New Folder
            </button>
          </div>
        )}

        {node.isFolder && isExpanded && hasChildren && (
          <div>
            {node.children!.map(child => renderNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full bg-white dark:bg-[#18181a] relative">
      <div className="p-2 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">Explorer</h3>
      </div>
      
      <div className="overflow-y-auto h-full">
        {fileTree.map(node => renderNode(node))}
        
        {Object.keys(files).length === 0 && (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <p className="text-sm mb-2">No files yet</p>
            <button
              onClick={() => handleCreateFile('')}
              className="text-xs bg-purple-600 text-white px-2 py-1 rounded hover:bg-purple-700"
            >
              Create your first file
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
