'use client';

import { useFileStore } from '@/stores/fileStore';
import { Message } from 'ai/react';

export type ActionType = 'file' | 'shell' | 'start';

export interface BaseAction {
  content: string;
}

export interface FileAction extends BaseAction {
  type: 'file';
  filePath: string;
}

export interface ShellAction extends BaseAction {
  type: 'shell';
}

export interface StartAction extends BaseAction {
  type: 'start';
}

export type BoltAction = FileAction | ShellAction | StartAction;

interface ParserCallbacks {
  onFileAction?: (action: FileAction) => void;
  onShellAction?: (action: ShellAction) => void;
  onStartAction?: (action: StartAction) => void;
}

export class StreamingMessageParser {
  private callbacks: ParserCallbacks;

  constructor(callbacks: ParserCallbacks = {}) {
    this.callbacks = callbacks;
  }

  parse(messageId: string, content: string) {
    console.log(`🔍 Parsing message ${messageId} for artifacts...`);
    console.log(`📄 Message content preview:`, content.substring(0, 200) + '...');

    // Parse boltArtifact tags - handle both formats
    const artifactRegex = /<boltArtifact[^>]*>([\s\S]*?)<\/boltArtifact>/g;
    let artifactMatch;
    let foundArtifacts = 0;

    while ((artifactMatch = artifactRegex.exec(content)) !== null) {
      foundArtifacts++;
      const fullMatch = artifactMatch[0];
      const artifactContent = artifactMatch[1];

      console.log(`📦 Found artifact ${foundArtifacts}:`, fullMatch.substring(0, 100) + '...');

      // Check if this is a simplified format (type="file" name="...")
      const simplifiedMatch = fullMatch.match(/<boltArtifact[^>]*type="file"[^>]*name="([^"]+)"[^>]*>/);
      if (simplifiedMatch) {
        const fileName = simplifiedMatch[1];
        console.log(`📄 Simplified format detected for file: ${fileName}`);

        if (this.callbacks.onFileAction) {
          this.callbacks.onFileAction({
            type: 'file',
            filePath: fileName,
            content: artifactContent.trim(),
          });
        }
      } else {
        // Standard format with boltAction tags
        this.parseActions(artifactContent);
      }
    }

    if (foundArtifacts === 0) {
      console.log(`ℹ️ No artifacts found in message ${messageId}`);
      console.log(`🔍 Checking for boltArtifact tags in content...`);
      if (content.includes('<boltArtifact')) {
        console.log(`⚠️ Found boltArtifact text but regex didn't match. Content:`, content);
      } else {
        console.log(`❌ No boltArtifact tags found in content at all`);
      }
    }
  }

  private parseActions(content: string) {
    // Parse boltAction tags - handle multiple formats
    const actionRegex = /<boltAction\s+([^>]+)>([\s\S]*?)<\/boltAction>/g;
    let actionMatch;
    let foundActions = 0;

    while ((actionMatch = actionRegex.exec(content)) !== null) {
      foundActions++;
      const [, attributes, actionContent] = actionMatch;

      // Parse attributes
      const typeMatch = attributes.match(/type="([^"]+)"/);
      const filePathMatch = attributes.match(/filePath="([^"]+)"/);
      const pathMatch = attributes.match(/path="([^"]+)"/);

      const type = typeMatch ? typeMatch[1] : '';
      const filePath = filePathMatch ? filePathMatch[1] : (pathMatch ? pathMatch[1] : '');

      console.log(`⚡ Found action ${foundActions}: type=${type}, filePath=${filePath}`);

      // Handle different type variations
      if (type === 'file' || type === 'createFile') {
        if (filePath && this.callbacks.onFileAction) {
          this.callbacks.onFileAction({
            type: 'file',
            filePath,
            content: actionContent.trim(),
          });
        }
      } else if (type === 'shell') {
        if (this.callbacks.onShellAction) {
          this.callbacks.onShellAction({
            type: 'shell',
            content: actionContent.trim(),
          });
        }
      } else if (type === 'start') {
        if (this.callbacks.onStartAction) {
          this.callbacks.onStartAction({
            type: 'start',
            content: actionContent.trim(),
          });
        }
      }
    }

    if (foundActions === 0) {
      console.log(`ℹ️ No actions found in artifact content`);
    }
  }
}

// Create a global message parser instance
const messageParser = new StreamingMessageParser({
  onFileAction: async (action: FileAction) => {
    const { addFile } = useFileStore.getState();
    try {
      await addFile(action.filePath, action.content);
      console.log(`✅ Created/updated file: ${action.filePath}`);
      console.log(`📄 Content preview: ${action.content.substring(0, 100)}...`);
    } catch (error) {
      console.error(`❌ Failed to create file ${action.filePath}:`, error);
    }
  },
  onShellAction: (action: ShellAction) => {
    console.log('Shell command:', action.content);
    // TODO: Integrate with terminal store to execute commands
  },
  onStartAction: (action: StartAction) => {
    console.log('Start command:', action.content);
    // TODO: Integrate with terminal store to execute start commands
  },
});

export const parseMessages = async (messages: Message[]) => {
  console.log(`🚀 parseMessages called with ${messages.length} messages`);
  for (const message of messages) {
    if (message.role === 'assistant') {
      console.log(`🤖 Parsing assistant message: ${message.id}`);
      console.log(`📋 Message object:`, message);
      console.log(`📝 Message content type:`, typeof message.content);
      console.log(`📝 Message content:`, message.content);
      messageParser.parse(message.id, message.content as string);
    }
  }
};

// Test function to verify parsing works
export const testMessageParser = () => {
  const testContent1 = `Here's a simple HTML file with 'Hello World' using the boltArtifact format:

<boltArtifact type="file" name="test.html">
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello World</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>
</boltArtifact>`;

  const testContent2 = `Here's a simple HTML file in the boltArtifact format:

<boltArtifact>
<boltAction type="createFile" path="index.html">
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello World</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>
</boltAction>
</boltArtifact>`;

  console.log('🧪 Testing message parser with format 1...');
  messageParser.parse('test-message-1', testContent1);

  console.log('🧪 Testing message parser with format 2...');
  messageParser.parse('test-message-2', testContent2);
};

// Make test function available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).testMessageParser = testMessageParser;
}

// Simple function to extract files from message content
export function parseMessageForFiles(content: string): Record<string, string> {
  const files: Record<string, string> = {};
  
  // Parse boltArtifact and boltAction tags
  const artifactRegex = /<boltArtifact[^>]*>([\s\S]*?)<\/boltArtifact>/g;
  let artifactMatch;

  while ((artifactMatch = artifactRegex.exec(content)) !== null) {
    const artifactContent = artifactMatch[1];
    
    // Extract file actions
    const fileActionRegex = /<boltAction\s+type="file"\s+filePath="([^"]+)"\s*>([\s\S]*?)<\/boltAction>/g;
    let fileMatch;

    while ((fileMatch = fileActionRegex.exec(artifactContent)) !== null) {
      const [, filePath, fileContent] = fileMatch;
      files[filePath] = fileContent.trim();
    }
  }

  return files;
}
