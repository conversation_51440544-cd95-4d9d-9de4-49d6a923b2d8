'use client';

import { useEffect, useRef, useState } from 'react';
import { EditorView, basicSetup } from 'codemirror';
import { EditorState } from '@codemirror/state';
import { javascript } from '@codemirror/lang-javascript';
import { html } from '@codemirror/lang-html';
import { css } from '@codemirror/lang-css';
import { json } from '@codemirror/lang-json';
import { markdown } from '@codemirror/lang-markdown';
import { python } from '@codemirror/lang-python';
import { oneDark } from '@codemirror/theme-one-dark';
import { useFileStore } from '../../../stores/fileStore';
import { useEditorStore } from '../../../stores/editorStore';
import useThemeStore from '@/stores/themeSlice';

interface EditorProps {
  fileName: string;
  initialLine?: number;
}

export function Editor({ fileName, initialLine }: EditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const { getContent, updateContent } = useFileStore();
  const { setDirty } = useEditorStore();
  const { isDarkMode } = useThemeStore();
  const [isInitialized, setIsInitialized] = useState(false);

  const getLanguageExtension = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return javascript({ jsx: true, typescript: extension.includes('ts') });
      case 'html':
      case 'htm':
        return html();
      case 'css':
      case 'scss':
      case 'sass':
        return css();
      case 'json':
        return json();
      case 'md':
      case 'markdown':
        return markdown();
      case 'py':
        return python();
      default:
        return javascript();
    }
  };

  useEffect(() => {
    if (!editorRef.current || isInitialized) return;

    const content = getContent(fileName);
    
    const extensions = [
      basicSetup,
      getLanguageExtension(fileName),
      EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          const newContent = update.state.doc.toString();
          updateContent(fileName, newContent);
          setDirty(fileName, true);
        }
      }),
      EditorView.theme({
        '&': {
          height: '100%',
          fontSize: '14px',
        },
        '.cm-content': {
          padding: '16px',
          minHeight: '100%',
        },
        '.cm-focused': {
          outline: 'none',
        },
        '.cm-editor': {
          height: '100%',
        },
        '.cm-scroller': {
          height: '100%',
        },
      }),
    ];

    if (isDarkMode) {
      extensions.push(oneDark);
    }

    const state = EditorState.create({
      doc: content,
      extensions,
    });

    const view = new EditorView({
      state,
      parent: editorRef.current,
    });

    viewRef.current = view;
    setIsInitialized(true);

    // Jump to specific line if provided
    if (initialLine && initialLine > 0) {
      setTimeout(() => {
        const line = Math.min(initialLine, view.state.doc.lines);
        const pos = view.state.doc.line(line).from;
        view.dispatch({
          selection: { anchor: pos, head: pos },
          effects: EditorView.scrollIntoView(pos, { y: 'center' }),
        });
        view.focus();
      }, 100);
    }

    return () => {
      view.destroy();
      setIsInitialized(false);
    };
  }, [fileName, isDarkMode]);

  // Update content when file changes externally
  useEffect(() => {
    if (!viewRef.current || !isInitialized) return;

    const currentContent = viewRef.current.state.doc.toString();
    const fileContent = getContent(fileName);
    
    if (currentContent !== fileContent) {
      viewRef.current.dispatch({
        changes: {
          from: 0,
          to: viewRef.current.state.doc.length,
          insert: fileContent,
        },
      });
    }
  }, [fileName, getContent, isInitialized]);

  return (
    <div className="h-full w-full bg-white dark:bg-[#18181a]">
      <div ref={editorRef} className="h-full w-full" />
    </div>
  );
}
