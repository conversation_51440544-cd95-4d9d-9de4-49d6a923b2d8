'use client';

import { Message, CreateMessage, ChatRequestOptions } from 'ai/react';

interface TipsProps {
  append: (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => void;
  setInput: (text: string) => void;
  handleFileSelect: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const Tips: React.FC<TipsProps> = ({ append, setInput, handleFileSelect }) => {
  const tips = [
    {
      title: "Build a React App",
      description: "Create a modern React application with components",
      prompt: "Build a React todo app with add, delete, and mark complete functionality using modern React hooks and Tailwind CSS styling."
    },
    {
      title: "Create a Landing Page",
      description: "Design a beautiful landing page",
      prompt: "Create a modern landing page for a SaaS product with hero section, features, pricing, and contact form using HTML, CSS, and JavaScript."
    },
    {
      title: "Build a Dashboard",
      description: "Create an admin dashboard interface",
      prompt: "Build a responsive admin dashboard with sidebar navigation, charts, tables, and cards using React and Tailwind CSS."
    },
    {
      title: "Make a Game",
      description: "Create an interactive game",
      prompt: "Create a simple Snake game using HTML5 Canvas and JavaScript with score tracking and game over functionality."
    }
  ];

  const handleTipClick = (prompt: string) => {
    setInput(prompt);
  };

  return (
    <div className="mb-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          What would you like to build?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Choose a template below or describe your own idea
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
        {tips.map((tip, index) => (
          <button
            key={index}
            onClick={() => handleTipClick(tip.prompt)}
            className="p-4 text-left bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-purple-300 dark:hover:border-purple-600 hover:shadow-md transition-all duration-200 group"
          >
            <h3 className="font-semibold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
              {tip.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {tip.description}
            </p>
          </button>
        ))}
      </div>
    </div>
  );
};

export default Tips;
