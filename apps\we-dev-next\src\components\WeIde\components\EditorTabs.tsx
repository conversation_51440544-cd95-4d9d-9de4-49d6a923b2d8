'use client';

import { X } from "lucide-react";
import { useEditorStore } from "../../../stores/editorStore";
import { cn } from "@/utils/cn";
import FileIcon from "./IDEContent/FileExplorer/components/fileIcon";

interface EditorTabsProps {
  openTabs: string[];
  activeTab: string;
  onTabSelect: (tab: string) => void;
  onTabClose: (tab: string) => void;
  onCloseAll: () => void;
}

export function EditorTabs({
  openTabs,
  activeTab,
  onTabSelect,
  onTabClose,
  onCloseAll,
}: EditorTabsProps) {
  const { isDirty } = useEditorStore();

  const handleTabClose = (tab: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onTabClose(tab);
  };

  const handleCloseAll = () => {
    onCloseAll();
  };

  const getFileName = (path: string) => {
    return path.split('/').pop() || path;
  };

  if (openTabs.length === 0) {
    return (
      <div className="h-10 bg-[#f8f8f8] dark:bg-[#1e1e1e] border-b border-[#e4e4e4] dark:border-[#333] flex items-center justify-center">
        <span className="text-sm text-gray-500 dark:text-gray-400">No files open</span>
      </div>
    );
  }

  return (
    <div className="h-10 bg-[#f8f8f8] dark:bg-[#1e1e1e] border-b border-[#e4e4e4] dark:border-[#333] flex items-center overflow-x-auto">
      <div className="flex">
        {openTabs.map((tab) => (
          <div
            key={tab}
            onClick={() => onTabSelect(tab)}
            className={cn(
              "flex items-center gap-2 px-3 py-2 text-sm cursor-pointer border-r border-[#e4e4e4] dark:border-[#333] min-w-0 max-w-[200px] group",
              activeTab === tab
                ? "bg-white dark:bg-[#18181a] text-[#333] dark:text-white"
                : "text-[#666] dark:text-gray-400 hover:bg-[#e4e4e4] dark:hover:bg-[#333]"
            )}
          >
            <FileIcon fileName={getFileName(tab)} />
            <span className="truncate flex-1">
              {getFileName(tab)}
              {isDirty[tab] && <span className="ml-1 text-orange-500">•</span>}
            </span>
            <button
              onClick={(e) => handleTabClose(tab, e)}
              className="opacity-0 group-hover:opacity-100 hover:bg-gray-200 dark:hover:bg-gray-600 rounded p-0.5 transition-all"
            >
              <X size={14} />
            </button>
          </div>
        ))}
      </div>
      
      {openTabs.length > 1 && (
        <button
          onClick={handleCloseAll}
          className="ml-auto mr-2 px-2 py-1 text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
          title="Close All"
        >
          Close All
        </button>
      )}
    </div>
  );
}
