/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?ad70\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjIzX0BvcGVudGVsZW1ldHJ5XzIzNWRmYzRlNzcxYzc2NTdkZGFlM2E5MjE1ODNkN2QxL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3Byb2dyYW1taW5nJTVDJTVDd2UwLW1haW4lNUMlNUNhcHBzJTVDJTVDd2UtZGV2LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjIzXyU0MG9wZW50ZWxlbWV0cnlfMjM1ZGZjNGU3NzFjNzY1N2RkYWUzYTkyMTU4M2Q3ZDElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNXb3Jrc3BhY2UlNUMlNUNwcm9ncmFtbWluZyU1QyU1Q3dlMC1tYWluJTVDJTVDYXBwcyU1QyU1Q3dlLWRldi1uZXh0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTQuMi4yM18lNDBvcGVudGVsZW1ldHJ5XzIzNWRmYzRlNzcxYzc2NTdkZGFlM2E5MjE1ODNkN2QxJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3Byb2dyYW1taW5nJTVDJTVDd2UwLW1haW4lNUMlNUNhcHBzJTVDJTVDd2UtZGV2LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjIzXyU0MG9wZW50ZWxlbWV0cnlfMjM1ZGZjNGU3NzFjNzY1N2RkYWUzYTkyMTU4M2Q3ZDElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDV29ya3NwYWNlJTVDJTVDcHJvZ3JhbW1pbmclNUMlNUN3ZTAtbWFpbiU1QyU1Q2FwcHMlNUMlNUN3ZS1kZXYtbmV4dCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Qy5wbnBtJTVDJTVDbmV4dCU0MDE0LjIuMjNfJTQwb3BlbnRlbGVtZXRyeV8yMzVkZmM0ZTc3MWM3NjU3ZGRhZTNhOTIxNTgzZDdkMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3Byb2dyYW1taW5nJTVDJTVDd2UwLW1haW4lNUMlNUNhcHBzJTVDJTVDd2UtZGV2LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjIzXyU0MG9wZW50ZWxlbWV0cnlfMjM1ZGZjNGU3NzFjNzY1N2RkYWUzYTkyMTU4M2Q3ZDElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNub3QtZm91bmQtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3Byb2dyYW1taW5nJTVDJTVDd2UwLW1haW4lNUMlNUNhcHBzJTVDJTVDd2UtZGV2LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjIzXyU0MG9wZW50ZWxlbWV0cnlfMjM1ZGZjNGU3NzFjNzY1N2RkYWUzYTkyMTU4M2Q3ZDElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrWUFBNE87QUFDNU87QUFDQSxvWUFBNk87QUFDN087QUFDQSwwWUFBZ1A7QUFDaFA7QUFDQSx3WUFBK087QUFDL087QUFDQSxrWkFBb1A7QUFDcFA7QUFDQSxzYUFBOFAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvPzUxMGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxccHJvZ3JhbW1pbmdcXFxcd2UwLW1haW5cXFxcYXBwc1xcXFx3ZS1kZXYtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjIzX0BvcGVudGVsZW1ldHJ5XzIzNWRmYzRlNzcxYzc2NTdkZGFlM2E5MjE1ODNkN2QxXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHByb2dyYW1taW5nXFxcXHdlMC1tYWluXFxcXGFwcHNcXFxcd2UtZGV2LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTQuMi4yM19Ab3BlbnRlbGVtZXRyeV8yMzVkZmM0ZTc3MWM3NjU3ZGRhZTNhOTIxNTgzZDdkMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxccHJvZ3JhbW1pbmdcXFxcd2UwLW1haW5cXFxcYXBwc1xcXFx3ZS1kZXYtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjIzX0BvcGVudGVsZW1ldHJ5XzIzNWRmYzRlNzcxYzc2NTdkZGFlM2E5MjE1ODNkN2QxXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXFdvcmtzcGFjZVxcXFxwcm9ncmFtbWluZ1xcXFx3ZTAtbWFpblxcXFxhcHBzXFxcXHdlLWRldi1uZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE0LjIuMjNfQG9wZW50ZWxlbWV0cnlfMjM1ZGZjNGU3NzFjNzY1N2RkYWUzYTkyMTU4M2Q3ZDFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxccHJvZ3JhbW1pbmdcXFxcd2UwLW1haW5cXFxcYXBwc1xcXFx3ZS1kZXYtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjIzX0BvcGVudGVsZW1ldHJ5XzIzNWRmYzRlNzcxYzc2NTdkZGFlM2E5MjE1ODNkN2QxXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxccHJvZ3JhbW1pbmdcXFxcd2UwLW1haW5cXFxcYXBwc1xcXFx3ZS1kZXYtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjIzX0BvcGVudGVsZW1ldHJ5XzIzNWRmYzRlNzcxYzc2NTdkZGFlM2E5MjE1ODNkN2QxXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.23_%40opentelemetry_235dfc4e771c7657ddae3a921583d7d1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjIzX0BvcGVudGVsZW1ldHJ5XzIzNWRmYzRlNzcxYzc2NTdkZGFlM2E5MjE1ODNkN2QxL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3Byb2dyYW1taW5nJTVDJTVDd2UwLW1haW4lNUMlNUNhcHBzJTVDJTVDd2UtZGV2LW5leHQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWdIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Lz85OTExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHByb2dyYW1taW5nXFxcXHdlMC1tYWluXFxcXGFwcHNcXFxcd2UtZGV2LW5leHRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cprogramming%5C%5Cwe0-main%5C%5Capps%5C%5Cwe-dev-next%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_App__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/App */ \"(ssr)/./src/components/App.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_App__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRW1DO0FBRXBCLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCx1REFBR0E7Ozs7O0FBQ2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9zcmMvYXBwL3BhZ2UudHN4P2Y2OGEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgQXBwIGZyb20gJ0AvY29tcG9uZW50cy9BcHAnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgcmV0dXJuIDxBcHAgLz47XG59XG4iXSwibmFtZXMiOlsiQXBwIiwiSG9tZVBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AiChat/index.tsx":
/*!*****************************************!*\
  !*** ./src/components/AiChat/index.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AiChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction AiChat() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col bg-white dark:bg-[#111] border-r border-gray-200 dark:border-[#333]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-purple-600 dark:text-purple-400\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                        children: \"AI Chat\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Chat functionality will be implemented here\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\index.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AiChat/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/App.tsx":
/*!********************************!*\
  !*** ./src/components/App.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../stores/userSlice */ \"(ssr)/./src/stores/userSlice.ts\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../stores/chatModeSlice */ \"(ssr)/./src/stores/chatModeSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserModal */ \"(ssr)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Header/index.tsx\");\n/* harmony import */ var _AiChat__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AiChat */ \"(ssr)/./src/components/AiChat/index.tsx\");\n/* harmony import */ var _Login__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Login */ \"(ssr)/./src/components/Login/index.tsx\");\n/* harmony import */ var _EditorPreviewTabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./EditorPreviewTabs */ \"(ssr)/./src/components/EditorPreviewTabs.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _types_chat__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../types/chat */ \"(ssr)/./src/types/chat.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _hooks_useInit__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/useInit */ \"(ssr)/./src/hooks/useInit.ts\");\n/* harmony import */ var _loading__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./loading */ \"(ssr)/./src/components/loading.tsx\");\n/* harmony import */ var _TopView__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TopView */ \"(ssr)/./src/components/TopView/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction App() {\n    const { mode, initOpen } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { isLoginModalOpen, closeLoginModal, openLoginModal, isLoading } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const { isDarkMode } = (0,_hooks_useInit__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TopView__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserModal__WEBPACK_IMPORTED_MODULE_3__.GlobalLimitModal, {\n                onLogin: openLoginModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Login__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isLoginModalOpen,\n                onClose: closeLoginModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_8___default()(\"h-screen w-screen flex flex-col overflow-hidden\", {\n                    dark: isDarkMode\n                }),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full h-full max-h-[calc(100%-48px)] bg-white dark:bg-[#111]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AiChat__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            mode === _types_chat__WEBPACK_IMPORTED_MODULE_9__.ChatMode.Builder && !initOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditorPreviewTabs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 54\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_10__.Toaster, {\n                position: \"top-center\",\n                toastOptions: {\n                    duration: 2000,\n                    style: {\n                        zIndex: 100000\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_loading__WEBPACK_IMPORTED_MODULE_12__.Loading, {}, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n                lineNumber: 49,\n                columnNumber: 21\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\App.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/App.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/EditorPreviewTabs.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPreviewTabs.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPreviewTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction EditorPreviewTabs() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col bg-white dark:bg-[#111]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-blue-600 dark:text-blue-400\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                        children: \"Editor & Preview\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Code editor and preview functionality will be implemented here\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\EditorPreviewTabs.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/EditorPreviewTabs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header/HeaderActions.tsx":
/*!*************************************************!*\
  !*** ./src/components/Header/HeaderActions.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderActions: () => (/* binding */ HeaderActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(ssr)/./src/stores/chatModeSlice.ts\");\n/* harmony import */ var _types_chat__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types/chat */ \"(ssr)/./src/types/chat.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var jszip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! jszip */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/index.js\");\n/* harmony import */ var jszip__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(jszip__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ HeaderActions auto */ \n\n\n\n\n\n\nfunction HeaderActions() {\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deployUrl, setDeployUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDeploying, setIsDeploying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleDownload = async ()=>{\n        try {\n            // Create a zip file with project files\n            const zip = new (jszip__WEBPACK_IMPORTED_MODULE_6___default())();\n            // TODO: Get actual project files from file store\n            // For now, create a sample file\n            zip.file(\"index.html\", `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>We0 Project</title>\n</head>\n<body>\n    <h1>Hello from We0!</h1>\n    <p>This is a sample project generated by We0.</p>\n</body>\n</html>`);\n            const content = await zip.generateAsync({\n                type: \"blob\"\n            });\n            // Create download link\n            const url = URL.createObjectURL(content);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"we0-project.zip\";\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Project downloaded successfully!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to download project\");\n        }\n    };\n    const publish = async ()=>{\n        setIsDeploying(true);\n        try {\n            // Create a zip file with project files for deployment\n            const zip = new (jszip__WEBPACK_IMPORTED_MODULE_6___default())();\n            // TODO: Get actual project files from file store\n            // For now, create a sample file\n            zip.file(\"index.html\", `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>We0 Project</title>\n</head>\n<body>\n    <h1>Hello from We0!</h1>\n    <p>This project was deployed using We0.</p>\n</body>\n</html>`);\n            const content = await zip.generateAsync({\n                type: \"blob\"\n            });\n            const file = new File([\n                content\n            ], \"project.zip\", {\n                type: \"application/zip\"\n            });\n            const result = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_5__.deployProject)(file);\n            if (result.success && result.url) {\n                setDeployUrl(result.url);\n                setShowModal(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Project deployed successfully!\");\n            } else {\n                throw new Error(result.message || \"Deployment failed\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Deployment failed\");\n            console.error(\"Deployment error:\", error);\n        } finally{\n            setIsDeploying(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [\n            mode === _types_chat__WEBPACK_IMPORTED_MODULE_4__.ChatMode.Builder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDownload,\n                        className: \"flex items-center gap-1.5 px-2.5 py-1.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-white/5 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Download\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: publish,\n                        disabled: isDeploying,\n                        className: \"flex items-center gap-1.5 px-2.5 py-1.5 text-sm text-white bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 rounded-lg transition-colors\",\n                        children: [\n                            isDeploying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 animate-spin\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        className: \"opacity-25\",\n                                        cx: \"12\",\n                                        cy: \"12\",\n                                        r: \"10\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        className: \"opacity-75\",\n                                        fill: \"currentColor\",\n                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isDeploying ? \"Deploying...\" : \"Deploy\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this),\n            showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl transform transition-all\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-5xl mb-4\",\n                                    children: \"\\uD83D\\uDE80\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                    children: \"Deployment Successful!\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mt-2\",\n                                    children: \"Your project has been deployed to the cloud\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-300 mb-1\",\n                                    children: \"Deployment URL:\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"flex-1 text-sm bg-white dark:bg-gray-800 px-2 py-1 rounded border text-purple-600 dark:text-purple-400\",\n                                            children: deployUrl\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                navigator.clipboard.writeText(deployUrl);\n                                                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"URL copied to clipboard!\");\n                                            },\n                                            className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowModal(false),\n                                    className: \"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.open(deployUrl, \"_blank\"),\n                                    className: \"px-4 py-2 bg-purple-600 dark:bg-purple-500 text-white rounded-lg hover:bg-purple-700 dark:hover:bg-purple-600 transition-colors flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Visit Site\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\HeaderActions.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header/HeaderActions.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header/ProjectTitle.tsx":
/*!************************************************!*\
  !*** ./src/components/Header/ProjectTitle.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectTitle: () => (/* binding */ ProjectTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../stores/userSlice */ \"(ssr)/./src/stores/userSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ ProjectTitle auto */ \n\n\nfunction ProjectTitle() {\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { user, isAuthenticated } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const getInitials = (name)=>{\n        return name?.split(\" \").map((word)=>word[0]).join(\"\").toUpperCase().slice(0, 2) || \"?\";\n    };\n    const handleMouseEnter = ()=>{\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        setIsSidebarOpen(true);\n    };\n    const handleMouseLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setIsSidebarOpen(false);\n        }, 300);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1.5 px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-white/10 transition-colors group\",\n                onMouseEnter: handleMouseEnter,\n                onMouseLeave: handleMouseLeave,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n          w-6 h-6 rounded-full\n          flex items-center justify-center\n          text-white text-xs font-medium\n          ${user?.avatar ? \"\" : \"bg-purple-500 dark:bg-purple-600\"}\n        `,\n                        style: user?.avatar ? {\n                            backgroundImage: `url(${user.avatar})`,\n                            backgroundSize: \"cover\"\n                        } : undefined,\n                        children: !user?.avatar && getInitials(user?.username || \"?\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-900 dark:text-white text-[14px] font-normal\",\n                        children: isAuthenticated ? user?.username : \"Guest\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-3 h-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-12 left-0 w-64 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-300\",\n                    children: \"Sidebar functionality will be implemented here\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\ProjectTitle.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header/ProjectTitle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header/index.tsx":
/*!*****************************************!*\
  !*** ./src/components/Header/index.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ProjectTitle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProjectTitle */ \"(ssr)/./src/components/Header/ProjectTitle.tsx\");\n/* harmony import */ var _HeaderActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HeaderActions */ \"(ssr)/./src/components/Header/HeaderActions.tsx\");\n/* harmony import */ var _barrel_optimize_names_FaCode_react_icons_fa6__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaCode!=!react-icons/fa6 */ \"(ssr)/./node_modules/.pnpm/react-icons@5.5.0_react@18.3.1/node_modules/react-icons/fa6/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"min-h-12 flex items-center px-4 h-12 bg-white dark:bg-[#18181a] border-b border-gray-200 dark:border-[#333333]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProjectTitle__WEBPACK_IMPORTED_MODULE_1__.ProjectTitle, {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-6 h-6 opacity-90 bg-gradient-to-br from-purple-500 to-purple-600 dark:from-blue-500 dark:to-purple-500 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCode_react_icons_fa6__WEBPACK_IMPORTED_MODULE_3__.FaCode, {\n                    className: \"text-[12px] text-white\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"ml-2 opacity-90 text-[18px] font-bold bg-gradient-to-r from-purple-500 to-purple-600 dark:from-blue-500 dark:to-purple-500 bg-clip-text text-transparent\",\n                children: \"We0\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HeaderActions__WEBPACK_IMPORTED_MODULE_2__.HeaderActions, {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Header\\\\index.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Login/index.tsx":
/*!****************************************!*\
  !*** ./src/components/Login/index.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Login({ isOpen, onClose }) {\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: \"Login\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"Enter your email\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Password\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"Enter your password\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors\",\n                            children: \"Login\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\Login\\\\index.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Login/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TopView/index.tsx":
/*!******************************************!*\
  !*** ./src/components/TopView/index.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TopViewContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction TopViewContainer({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\TopView\\\\index.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ub3BWaWV3L2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBUWUsU0FBU0EsaUJBQWlCLEVBQUVDLFFBQVEsRUFBeUI7SUFDMUUscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ1pGOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL3NyYy9jb21wb25lbnRzL1RvcFZpZXcvaW5kZXgudHN4PzU5NjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBUb3BWaWV3Q29udGFpbmVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUb3BWaWV3Q29udGFpbmVyKHsgY2hpbGRyZW4gfTogVG9wVmlld0NvbnRhaW5lclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsXCI+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiVG9wVmlld0NvbnRhaW5lciIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TopView/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UserModal/index.tsx":
/*!********************************************!*\
  !*** ./src/components/UserModal/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalLimitModal: () => (/* binding */ GlobalLimitModal),\n/* harmony export */   useLimitModalStore: () => (/* binding */ useLimitModalStore)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@5.0.3_@types+react@_b2b6531215a5b85a863ad74852a0d602/node_modules/zustand/esm/react.mjs\");\n/* __next_internal_client_entry_do_not_use__ useLimitModalStore,GlobalLimitModal auto */ \n\n// Create global state management\nconst useLimitModalStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set)=>({\n        isVisible: false,\n        type: \"login\",\n        openModal: (type)=>set({\n                isVisible: true,\n                type\n            }),\n        closeModal: ()=>set({\n                isVisible: false\n            })\n    }));\nfunction GlobalLimitModal({ onLogin }) {\n    const { isVisible, type, closeModal } = useLimitModalStore();\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                        children: type === \"login\" ? \"Login Required\" : \"Usage Limit Reached\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                        children: type === \"login\" ? \"Please log in to continue using the application.\" : \"You have reached your usage limit. Please upgrade your plan.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: closeModal,\n                                className: \"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (type === \"login\") {\n                                        onLogin();\n                                    }\n                                    closeModal();\n                                },\n                                className: \"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                                children: type === \"login\" ? \"Login\" : \"Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\UserModal\\\\index.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UserModal/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/loading.tsx":
/*!************************************!*\
  !*** ./src/components/loading.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loading: () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Loading auto */ \nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\loading.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-900 dark:text-white\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\loading.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\loading.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRU8sU0FBU0E7SUFDZCxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFLRCxXQUFVOzhCQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJeEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9zcmMvY29tcG9uZW50cy9sb2FkaW5nLnRzeD8wMzA3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuZXhwb3J0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgcC02IGZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTYgdy02IGJvcmRlci1iLTIgYm9yZGVyLXB1cnBsZS02MDBcIj48L2Rpdj5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5Mb2FkaW5nLi4uPC9zcGFuPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useInit.ts":
/*!******************************!*\
  !*** ./src/hooks/useInit.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _stores_themeSlice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/themeSlice */ \"(ssr)/./src/stores/themeSlice.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/stores/userSlice */ \"(ssr)/./src/stores/userSlice.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst useInit = ()=>{\n    const { isDarkMode, setTheme } = (0,_stores_themeSlice__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n    const { fetchUser } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Fetch user info if token exists\n        const fetchUserInfo = async ()=>{\n            if (false) {}\n        };\n        fetchUserInfo();\n        // Initialize language settings\n        if (false) {}\n        // Copy functionality\n        const callback = (event)=>{\n            try {\n                const selection = window.getSelection();\n                if (selection) {\n                    navigator.clipboard.writeText(selection.toString().trim()).then(()=>{\n                    // Copy successful\n                    });\n                }\n                event.preventDefault();\n            } catch (e) {\n            // Handle error\n            }\n        };\n        document.addEventListener(\"copy\", callback);\n        return ()=>document.removeEventListener(\"copy\", callback);\n    }, [\n        fetchUser\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        isDarkMode,\n        setTheme\n    ]);\n    return {\n        isDarkMode\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useInit);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlSW5pdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs2REFFZ0Q7QUFDRjtBQUNaO0FBRWxDLE1BQU1HLFVBQVU7SUFDWixNQUFNLEVBQUVDLFVBQVUsRUFBRUMsUUFBUSxFQUFFLEdBQUdMLDhEQUFhQTtJQUM5QyxNQUFNLEVBQUVNLFNBQVMsRUFBRSxHQUFHTCw2REFBWUE7SUFFbENDLGdEQUFTQSxDQUFDO1FBQ04sa0NBQWtDO1FBQ2xDLE1BQU1LLGdCQUFnQjtZQUNsQixJQUFJLEtBQWtCLEVBQWEsRUFnQmxDO1FBQ0w7UUFFQUE7UUFFQSwrQkFBK0I7UUFDL0IsSUFBSSxLQUFrQixFQUFhLEVBYWxDO1FBRUQscUJBQXFCO1FBQ3JCLE1BQU1rQixXQUFXLENBQUNDO1lBQ2QsSUFBSTtnQkFDQSxNQUFNQyxZQUFZQyxPQUFPQyxZQUFZO2dCQUNyQyxJQUFJRixXQUFXO29CQUNYTixVQUFVUyxTQUFTLENBQ2RDLFNBQVMsQ0FBQ0osVUFBVUssUUFBUSxHQUFHQyxJQUFJLElBQ25DQyxJQUFJLENBQUM7b0JBQ0Ysa0JBQWtCO29CQUN0QjtnQkFDUjtnQkFDQVIsTUFBTVMsY0FBYztZQUN4QixFQUFFLE9BQU9sQixHQUFHO1lBQ1IsZUFBZTtZQUNuQjtRQUNKO1FBRUFtQixTQUFTQyxnQkFBZ0IsQ0FBQyxRQUFRWjtRQUNsQyxPQUFPLElBQU1XLFNBQVNFLG1CQUFtQixDQUFDLFFBQVFiO0lBQ3RELEdBQUc7UUFBQ25CO0tBQVU7SUFFZEosZ0RBQVNBLENBQUM7UUFDTixJQUFJLEtBQWtCLEVBQWEsRUEwQmxDO0lBQ0wsR0FBRztRQUFDRTtRQUFZQztLQUFTO0lBRXpCLE9BQU87UUFBRUQ7SUFBVztBQUN4QjtBQUVBLGlFQUFlRCxPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vc3JjL2hvb2tzL3VzZUluaXQudHM/ZGJiMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB1c2VUaGVtZVN0b3JlIGZyb20gXCJAL3N0b3Jlcy90aGVtZVNsaWNlXCI7XG5pbXBvcnQgdXNlVXNlclN0b3JlIGZyb20gXCJAL3N0b3Jlcy91c2VyU2xpY2VcIjtcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuXG5jb25zdCB1c2VJbml0ID0gKCk6IHsgaXNEYXJrTW9kZTogYm9vbGVhbiB9ID0+IHtcbiAgICBjb25zdCB7IGlzRGFya01vZGUsIHNldFRoZW1lIH0gPSB1c2VUaGVtZVN0b3JlKCk7XG4gICAgY29uc3QgeyBmZXRjaFVzZXIgfSA9IHVzZVVzZXJTdG9yZSgpO1xuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgLy8gRmV0Y2ggdXNlciBpbmZvIGlmIHRva2VuIGV4aXN0c1xuICAgICAgICBjb25zdCBmZXRjaFVzZXJJbmZvID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgbGV0IHVzZXJJblRva2VuID0gJyc7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdXNlclN0b3JhZ2UgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcInVzZXItc3RvcmFnZVwiKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHVzZXJTdG9yYWdlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKHVzZXJTdG9yYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJJblRva2VuID0gcGFyc2VkPy5zdGF0ZT8udG9rZW47XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBwYXJzaW5nIGVycm9yXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJ0b2tlblwiKSB8fCB1c2VySW5Ub2tlbjtcbiAgICAgICAgICAgICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgICAgICAgICAgICAgZmV0Y2hVc2VyKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBcbiAgICAgICAgZmV0Y2hVc2VySW5mbygpO1xuXG4gICAgICAgIC8vIEluaXRpYWxpemUgbGFuZ3VhZ2Ugc2V0dGluZ3NcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBjb25zdCBzZXR0aW5nc0NvbmZpZyA9IEpTT04ucGFyc2UoXG4gICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJzZXR0aW5nc0NvbmZpZ1wiKSB8fCBcInt9XCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGlmICghc2V0dGluZ3NDb25maWcubGFuZ3VhZ2UpIHtcbiAgICAgICAgICAgICAgICAvLyBHZXQgYnJvd3NlciBsYW5ndWFnZSBzZXR0aW5nc1xuICAgICAgICAgICAgICAgIGNvbnN0IGJyb3dzZXJMYW5nID0gbmF2aWdhdG9yLmxhbmd1YWdlLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgICAgICAgICAgLy8gSWYgQ2hpbmVzZSBlbnZpcm9ubWVudCwgc2V0IHRvIENoaW5lc2UsIG90aGVyd2lzZSBzZXQgdG8gRW5nbGlzaFxuICAgICAgICAgICAgICAgIGNvbnN0IGRlZmF1bHRMYW5nID0gYnJvd3Nlckxhbmcuc3RhcnRzV2l0aChcInpoXCIpID8gXCJ6aFwiIDogXCJlblwiO1xuICAgICAgICAgICAgICAgIC8vIFRPRE86IEltcGxlbWVudCBpMThuIGxhbmd1YWdlIGNoYW5nZVxuICAgICAgICAgICAgICAgIC8vIGkxOG4uY2hhbmdlTGFuZ3VhZ2UoZGVmYXVsdExhbmcpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ29weSBmdW5jdGlvbmFsaXR5XG4gICAgICAgIGNvbnN0IGNhbGxiYWNrID0gKGV2ZW50OiBDbGlwYm9hcmRFdmVudCkgPT4ge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBzZWxlY3Rpb24gPSB3aW5kb3cuZ2V0U2VsZWN0aW9uKCk7XG4gICAgICAgICAgICAgICAgaWYgKHNlbGVjdGlvbikge1xuICAgICAgICAgICAgICAgICAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkXG4gICAgICAgICAgICAgICAgICAgICAgICAud3JpdGVUZXh0KHNlbGVjdGlvbi50b1N0cmluZygpLnRyaW0oKSlcbiAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBDb3B5IHN1Y2Nlc3NmdWxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBlcnJvclxuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBcbiAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcImNvcHlcIiwgY2FsbGJhY2spO1xuICAgICAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImNvcHlcIiwgY2FsbGJhY2spO1xuICAgIH0sIFtmZXRjaFVzZXJdKTtcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgaWYgKGlzRGFya01vZGUpIHtcbiAgICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZChcImRhcmtcIik7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QucmVtb3ZlKFwiZGFya1wiKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgY29uc3QgbXFsID0gd2luZG93Lm1hdGNoTWVkaWEoXCIocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspXCIpO1xuICAgICAgICAgICAgY29uc3Qgc2F2ZWRUaGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwidGhlbWVcIikgfHwgXCJzeXN0ZW1cIjtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgaWYgKHNhdmVkVGhlbWUgIT09IFwic3lzdGVtXCIpIHtcbiAgICAgICAgICAgICAgICBzZXRUaGVtZShzYXZlZFRoZW1lID09PSBcImRhcmtcIik7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHNldFRoZW1lKG1xbC5tYXRjaGVzKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgY29uc3QgaGFuZGxlU3RvcmFnZUNoYW5nZSA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoc2F2ZWRUaGVtZSA9PT0gXCJzeXN0ZW1cIikge1xuICAgICAgICAgICAgICAgICAgICBzZXRUaGVtZShtcWwubWF0Y2hlcyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgbXFsLmFkZEV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgaGFuZGxlU3RvcmFnZUNoYW5nZSk7XG4gICAgICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgICAgIG1xbC5yZW1vdmVFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sIFtpc0RhcmtNb2RlLCBzZXRUaGVtZV0pO1xuXG4gICAgcmV0dXJuIHsgaXNEYXJrTW9kZSB9O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgdXNlSW5pdDtcbiJdLCJuYW1lcyI6WyJ1c2VUaGVtZVN0b3JlIiwidXNlVXNlclN0b3JlIiwidXNlRWZmZWN0IiwidXNlSW5pdCIsImlzRGFya01vZGUiLCJzZXRUaGVtZSIsImZldGNoVXNlciIsImZldGNoVXNlckluZm8iLCJ1c2VySW5Ub2tlbiIsInVzZXJTdG9yYWdlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsInN0YXRlIiwidG9rZW4iLCJlIiwic2V0dGluZ3NDb25maWciLCJsYW5ndWFnZSIsImJyb3dzZXJMYW5nIiwibmF2aWdhdG9yIiwidG9Mb3dlckNhc2UiLCJkZWZhdWx0TGFuZyIsInN0YXJ0c1dpdGgiLCJjYWxsYmFjayIsImV2ZW50Iiwic2VsZWN0aW9uIiwid2luZG93IiwiZ2V0U2VsZWN0aW9uIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwidG9TdHJpbmciLCJ0cmltIiwidGhlbiIsInByZXZlbnREZWZhdWx0IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInJlbW92ZSIsIm1xbCIsIm1hdGNoTWVkaWEiLCJzYXZlZFRoZW1lIiwibWF0Y2hlcyIsImhhbmRsZVN0b3JhZ2VDaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useInit.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE: () => (/* binding */ API_BASE),\n/* harmony export */   ModelTypes: () => (/* binding */ ModelTypes),\n/* harmony export */   deployProject: () => (/* binding */ deployProject),\n/* harmony export */   enhancePrompt: () => (/* binding */ enhancePrompt),\n/* harmony export */   fetchModelOptions: () => (/* binding */ fetchModelOptions)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ API_BASE,ModelTypes,fetchModelOptions,enhancePrompt,deployProject auto */ // API configuration for the web environment\nconst API_BASE =  false ? 0 : \"\";\nvar ModelTypes;\n(function(ModelTypes) {\n    ModelTypes[\"Claude37sonnet\"] = \"claude-3-7-sonnet-20250219\";\n    ModelTypes[\"Claude35sonnet\"] = \"claude-3-5-sonnet-20240620\";\n    ModelTypes[\"gpt4oMini\"] = \"gpt-4o-mini\";\n    ModelTypes[\"DeepseekR1\"] = \"DeepSeek-R1\";\n    ModelTypes[\"DeepseekV3\"] = \"deepseek-chat\";\n})(ModelTypes || (ModelTypes = {}));\n// Fetch model configurations from the API\nasync function fetchModelOptions() {\n    try {\n        const response = await fetch(`${API_BASE}/api/model`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch model options\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching model options:\", error);\n        // Return default options if API fails\n        return [\n            {\n                label: \"Claude 3.5 Sonnet\",\n                value: \"claude-3-5-sonnet-20240620\",\n                useImage: true,\n                description: \"Claude 3.5 Sonnet\",\n                icon: null,\n                provider: \"claude\",\n                functionCall: true\n            }\n        ];\n    }\n}\n// Enhanced prompt API call\nasync function enhancePrompt(text) {\n    try {\n        const response = await fetch(`${API_BASE}/api/enhancedPrompt`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                text\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to enhance prompt\");\n        }\n        const data = await response.json();\n        return data.text || text;\n    } catch (error) {\n        console.error(\"Error enhancing prompt:\", error);\n        return text; // Return original text if enhancement fails\n    }\n}\n// Deploy project API call\nasync function deployProject(file) {\n    try {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const response = await fetch(`${API_BASE}/api/deploy`, {\n            method: \"POST\",\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to deploy project\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deploying project:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/chatModeSlice.ts":
/*!*************************************!*\
  !*** ./src/stores/chatModeSlice.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMode: () => (/* binding */ ChatMode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@5.0.3_@types+react@_b2b6531215a5b85a863ad74852a0d602/node_modules/zustand/esm/react.mjs\");\n\nvar ChatMode;\n(function(ChatMode) {\n    ChatMode[\"Chat\"] = \"chat\";\n    ChatMode[\"Builder\"] = \"builder\";\n})(ChatMode || (ChatMode = {}));\nconst useChatModeStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        mode: \"builder\",\n        initOpen: false,\n        setInitOpen: (initOpen)=>set({\n                initOpen\n            }),\n        setMode: (mode)=>set({\n                mode\n            })\n    }));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useChatModeStore);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmVzL2NoYXRNb2RlU2xpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDOztVQUU1QkM7OztHQUFBQSxhQUFBQTtBQVlMLE1BQU1DLG1CQUFtQkYsK0NBQU1BLENBQWdCLENBQUNHLE1BQVM7UUFDdkRDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxhQUFhLENBQUNELFdBQWFGLElBQUk7Z0JBQUVFO1lBQVM7UUFDMUNFLFNBQVMsQ0FBQ0gsT0FBU0QsSUFBSTtnQkFBRUM7WUFBSztJQUNoQztBQUVBLGlFQUFlRixnQkFBZ0JBLEVBQUM7QUFDWiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL3NyYy9zdG9yZXMvY2hhdE1vZGVTbGljZS50cz9kOTZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnO1xuXG5lbnVtIENoYXRNb2RlIHtcbiAgQ2hhdCA9ICdjaGF0JyxcbiAgQnVpbGRlciA9ICdidWlsZGVyJ1xufVxuXG5pbnRlcmZhY2UgQ2hhdE1vZGVTdGF0ZSB7XG4gIG1vZGU6IENoYXRNb2RlO1xuICBpbml0T3BlbjogYm9vbGVhbjtcbiAgc2V0SW5pdE9wZW46IChpbml0T3BlbjogYm9vbGVhbikgPT4gdm9pZDtcbiAgc2V0TW9kZTogKG1vZGU6IENoYXRNb2RlKSA9PiB2b2lkO1xufVxuXG5jb25zdCB1c2VDaGF0TW9kZVN0b3JlID0gY3JlYXRlPENoYXRNb2RlU3RhdGU+KChzZXQpID0+ICh7XG4gIG1vZGU6IENoYXRNb2RlLkJ1aWxkZXIsXG4gIGluaXRPcGVuOiBmYWxzZSxcbiAgc2V0SW5pdE9wZW46IChpbml0T3BlbikgPT4gc2V0KHsgaW5pdE9wZW4gfSksXG4gIHNldE1vZGU6IChtb2RlKSA9PiBzZXQoeyBtb2RlIH0pLFxufSkpO1xuXG5leHBvcnQgZGVmYXVsdCB1c2VDaGF0TW9kZVN0b3JlO1xuZXhwb3J0IHsgQ2hhdE1vZGUgfTtcbiJdLCJuYW1lcyI6WyJjcmVhdGUiLCJDaGF0TW9kZSIsInVzZUNoYXRNb2RlU3RvcmUiLCJzZXQiLCJtb2RlIiwiaW5pdE9wZW4iLCJzZXRJbml0T3BlbiIsInNldE1vZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/chatModeSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/themeSlice.ts":
/*!**********************************!*\
  !*** ./src/stores/themeSlice.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@5.0.3_@types+react@_b2b6531215a5b85a863ad74852a0d602/node_modules/zustand/esm/react.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst useThemeStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        isDarkMode: false,\n        toggleTheme: ()=>set((state)=>({\n                    isDarkMode: !state.isDarkMode\n                })),\n        setTheme: (isDark)=>set({\n                isDarkMode: isDark\n            })\n    }));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useThemeStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmVzL3RoZW1lU2xpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRWlDO0FBUWpDLE1BQU1DLGdCQUFnQkQsK0NBQU1BLENBQWEsQ0FBQ0UsTUFBUztRQUNqREMsWUFBWTtRQUNaQyxhQUFhLElBQU1GLElBQUksQ0FBQ0csUUFBVztvQkFBRUYsWUFBWSxDQUFDRSxNQUFNRixVQUFVO2dCQUFDO1FBQ25FRyxVQUFVLENBQUNDLFNBQW9CTCxJQUFJO2dCQUFFQyxZQUFZSTtZQUFPO0lBQzFEO0FBRUEsaUVBQWVOLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9zcmMvc3RvcmVzL3RoZW1lU2xpY2UudHM/MWU4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnO1xuXG5pbnRlcmZhY2UgVGhlbWVTdGF0ZSB7XG4gIGlzRGFya01vZGU6IGJvb2xlYW47XG4gIHRvZ2dsZVRoZW1lOiAoKSA9PiB2b2lkO1xuICBzZXRUaGVtZTogKGlzRGFyazogYm9vbGVhbikgPT4gdm9pZDtcbn1cblxuY29uc3QgdXNlVGhlbWVTdG9yZSA9IGNyZWF0ZTxUaGVtZVN0YXRlPigoc2V0KSA9PiAoe1xuICBpc0RhcmtNb2RlOiBmYWxzZSxcbiAgdG9nZ2xlVGhlbWU6ICgpID0+IHNldCgoc3RhdGUpID0+ICh7IGlzRGFya01vZGU6ICFzdGF0ZS5pc0RhcmtNb2RlIH0pKSxcbiAgc2V0VGhlbWU6IChpc0Rhcms6IGJvb2xlYW4pID0+IHNldCh7IGlzRGFya01vZGU6IGlzRGFyayB9KSxcbn0pKTtcblxuZXhwb3J0IGRlZmF1bHQgdXNlVGhlbWVTdG9yZTtcbiJdLCJuYW1lcyI6WyJjcmVhdGUiLCJ1c2VUaGVtZVN0b3JlIiwic2V0IiwiaXNEYXJrTW9kZSIsInRvZ2dsZVRoZW1lIiwic3RhdGUiLCJzZXRUaGVtZSIsImlzRGFyayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/themeSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/userSlice.ts":
/*!*********************************!*\
  !*** ./src/stores/userSlice.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TierType: () => (/* binding */ TierType),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@5.0.3_@types+react@_b2b6531215a5b85a863ad74852a0d602/node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/.pnpm/zustand@5.0.3_@types+react@_b2b6531215a5b85a863ad74852a0d602/node_modules/zustand/esm/middleware.mjs\");\n/* __next_internal_client_entry_do_not_use__ TierType,default auto */ \n\nvar TierType;\n(function(TierType) {\n    TierType[\"FREE\"] = \"free\";\n    TierType[\"PRO\"] = \"pro\";\n    TierType[\"PROMAX\"] = \"promax\";\n})(TierType || (TierType = {}));\nconst useUserStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        rememberMe: false,\n        isLoginModalOpen: false,\n        isLoading: false,\n        setRememberMe: (remember)=>{\n            if (false) {}\n            set({\n                rememberMe: remember\n            });\n        },\n        setUser: (user)=>{\n            if (false) {}\n            set(()=>({\n                    user,\n                    isAuthenticated: !!user\n                }));\n        },\n        setToken: (token)=>{\n            if (false) {}\n            set(()=>({\n                    token\n                }));\n        },\n        fetchUser: async ()=>{\n            set(()=>({\n                    isLoading: true\n                }));\n            try {\n                if (false) {}\n            } catch (error) {\n                console.error(error);\n            } finally{\n                set(()=>({\n                        isLoading: false\n                    }));\n            }\n        },\n        login: (user, token)=>{\n            if (false) {}\n            set(()=>({\n                    user,\n                    token,\n                    isAuthenticated: true,\n                    isLoginModalOpen: false\n                }));\n        },\n        logout: ()=>{\n            if (false) {}\n            set(()=>({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    rememberMe: false\n                }));\n        },\n        updateUser: (userData)=>set((state)=>{\n                const newUser = state.user ? {\n                    ...state.user,\n                    ...userData\n                } : null;\n                if (false) {}\n                return {\n                    user: newUser\n                };\n            }),\n        openLoginModal: ()=>set(()=>({\n                    isLoginModalOpen: true\n                })),\n        closeLoginModal: ()=>set(()=>({\n                    isLoginModalOpen: false\n                }))\n    }), {\n    name: \"user-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token,\n            isAuthenticated: state.isAuthenticated,\n            rememberMe: state.rememberMe\n        }),\n    version: 1,\n    onRehydrateStorage: ()=>(state)=>{\n            if (false) {}\n        }\n}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useUserStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/userSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/chat.ts":
/*!***************************!*\
  !*** ./src/types/chat.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMode: () => (/* binding */ ChatMode)\n/* harmony export */ });\nvar ChatMode;\n(function(ChatMode) {\n    ChatMode[\"Chat\"] = \"chat\";\n    ChatMode[\"Builder\"] = \"builder\";\n})(ChatMode || (ChatMode = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdHlwZXMvY2hhdC50cyIsIm1hcHBpbmdzIjoiOzs7OztVQUFZQTs7O0dBQUFBLGFBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vc3JjL3R5cGVzL2NoYXQudHM/MTNlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZW51bSBDaGF0TW9kZSB7XG4gIENoYXQgPSAnY2hhdCcsXG4gIEJ1aWxkZXIgPSAnYnVpbGRlcidcbn1cblxuZXhwb3J0IHR5cGUgQWN0aW9uVHlwZSA9ICdmaWxlJyB8ICdzaGVsbCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQmFzZUFjdGlvbiB7XG4gIGNvbnRlbnQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBGaWxlQWN0aW9uIGV4dGVuZHMgQmFzZUFjdGlvbiB7XG4gIHR5cGU6ICdmaWxlJztcbiAgZmlsZVBhdGg6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTaGVsbEFjdGlvbiBleHRlbmRzIEJhc2VBY3Rpb24ge1xuICB0eXBlOiAnc2hlbGwnO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN0YXJ0QWN0aW9uIGV4dGVuZHMgQmFzZUFjdGlvbiB7XG4gIHR5cGU6ICdzdGFydCc7XG59XG5cbmV4cG9ydCB0eXBlIEJvbHRBY3Rpb24gPSBGaWxlQWN0aW9uIHwgU2hlbGxBY3Rpb24gfCBTdGFydEFjdGlvbjtcblxuZXhwb3J0IHR5cGUgQm9sdEFjdGlvbkRhdGEgPSBCb2x0QWN0aW9uIHwgQmFzZUFjdGlvbjtcbiJdLCJuYW1lcyI6WyJDaGF0TW9kZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/types/chat.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e5f03a792056\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vc3JjL2FwcC9nbG9iYWxzLmNzcz84MTVjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTVmMDNhNzkyMDU2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"We0 - AI Development Platform\",\n    description: \"AI-powered development platform combining chat and code building capabilities\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFDN0JLOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdXZTAgLSBBSSBEZXZlbG9wbWVudCBQbGF0Zm9ybScsXG4gIGRlc2NyaXB0aW9uOiAnQUktcG93ZXJlZCBkZXZlbG9wbWVudCBwbGF0Zm9ybSBjb21iaW5pbmcgY2hhdCBhbmQgY29kZSBidWlsZGluZyBjYXBhYmlsaXRpZXMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Workspace\programming\we0-main\apps\we-dev-next\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/jszip@3.10.1","vendor-chunks/pako@1.0.11","vendor-chunks/readable-stream@2.3.8","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/react-icons@5.5.0_react@18.3.1","vendor-chunks/zustand@5.0.3_@types+react@_b2b6531215a5b85a863ad74852a0d602","vendor-chunks/inherits@2.0.4","vendor-chunks/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/classnames@2.5.1","vendor-chunks/util-deprecate@1.0.2","vendor-chunks/string_decoder@1.1.1","vendor-chunks/safe-buffer@5.1.2","vendor-chunks/process-nextick-args@2.0.1","vendor-chunks/lie@3.3.0","vendor-chunks/isarray@1.0.0","vendor-chunks/immediate@3.0.6","vendor-chunks/core-util-is@1.0.3"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();