'use client';

import { useEffect, useMemo, useRef, useState } from "react";
import { Message, useChat } from "ai/react";
import { toast } from "react-hot-toast";
import useChatStore, { IModelOption } from "../../../stores/chatSlice";
import { useFileStore } from "../../../stores/fileStore";
import { db } from "../../../utils/indexDB";
import { v4 as uuidv4 } from "uuid";
import { eventEmitter } from "../../../utils/EventEmitter";
import { MessageItem } from "./components/MessageItem";
import { ChatInput } from "./components/ChatInput";
import Tips from "./components/Tips";
import { parseMessage } from "../../../utils/messagepParseJson";
import useUserStore from "../../../stores/userSlice";
import { useLimitModalStore } from "../../UserModal";
import useChatModeStore from "../../../stores/chatModeSlice";
import useTerminalStore from "@/stores/terminalSlice";
import { ModelTypes } from "@/lib/api";

type WeMessages = (Message & {
    experimental_attachments?: Array<{
        id: string;
        name: string;
        type: string;
        localUrl: string;
        contentType: string;
        url: string;
    }>
})[]

export const BaseChat = ({ uuid: propUuid }: { uuid?: string }) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = useChatStore();
    const [checkCount, setCheckCount] = useState(0);
    const [visible, setVisible] = useState(false);
    const [baseModal, setBaseModal] = useState<IModelOption>({
        value: ModelTypes.Claude35sonnet,
        label: "Claude 3.5 Sonnet",
        useImage: true,
        from: "default",
        quota: 2,
        functionCall: true,
    });

    const { files } = useFileStore();
    const { user, token } = useUserStore();
    const { openModal } = useLimitModalStore();
    const { mode } = useChatModeStore();
    const [messages, setMessagesa] = useState<WeMessages>([]);
    const [isUploading, setIsUploading] = useState(false);
    const [errors, setErrors] = useState<any[]>([]);
    const parseTimeRef = useRef(Date.now());
    const refUuidMessages = useRef<string[]>([]);

    const chatUuid = propUuid || uuidv4();
    const baseChatUrl = typeof window !== 'undefined' ? window.location.origin : '';

    const clearErrors = () => setErrors([]);

    const scrollToBottom = () => {
        setTimeout(() => {
            messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
        }, 100);
    };

    // Mock functions for web environment
    const parseMessages = (messages: Message[]) => {
        // This would parse messages and update file system in desktop app
        // For web, we'll just log for now
        console.log('Parsing messages:', messages);
    };

    const updateFileSystemNow = () => {
        // This would update the file system in desktop app
        // For web, this is a no-op
        return Promise.resolve();
    };

    const createMpIcon = (files: Record<string, string>) => {
        // This would create mini program icons in desktop app
        // For web, this is a no-op
    };

    const checkExecList = () => {
        // This would check execution list in desktop app
        return [];
    };

    const checkFinish = () => {
        // This would check if execution is finished in desktop app
        return true;
    };

    const {
        messages: realMessages,
        input,
        handleInputChange,
        isLoading,
        setMessages,
        append,
        setInput,
        stop,
        reload,
    } = useChat({
        api: `${baseChatUrl}/api/chat`,
        headers: {
            ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: {
            model: baseModal.value,
            mode: mode,
            otherConfig: {
                ...otherConfig,
                extra: {
                    ...otherConfig.extra,
                    isBackEnd: otherConfig.isBackEnd,
                    backendLanguage: otherConfig.backendLanguage
                },
            },
        },
        onFinish: async (message) => {
            try {
                const initMessage = messages.filter(m => m.role === 'system');
                await db.insert(chatUuid, {
                    messages: [...messages, ...initMessage, message],
                    title:
                        [...initMessage, ...messages]
                            .find(
                                (m) => m.role === "user" && !m.content.includes("<boltArtifact")
                            )
                            ?.content?.slice(0, 50) || "New Chat",
                });
            } catch (error) {
                console.error("Failed to save chat history:", error);
            }
            setCheckCount(checkCount => checkCount + 1);
        },
        onError: (error: any) => {
            const msg = error?.errors?.[0]?.responseBody || String(error);
            console.log("error", error, msg);
            toast.error(msg);
            if (String(error).includes("Quota not enough")) {
                openModal('limit');
            }
            if (String(error).includes("Authentication required")) {
                openModal("login");
            }
        },
    });

    const filterMessages = useMemo(() => {
        return messages.filter((message) => {
            if (message.role === "system") return false;
            return true;
        });
    }, [messages]);

    useEffect(() => {
        if (Date.now() - parseTimeRef.current > 200 && isLoading) {
            setMessagesa(realMessages as WeMessages);
            parseTimeRef.current = Date.now();

            const needParseMessages = messages.filter(
                (m) => !refUuidMessages.current.includes(m.id)
            );
            parseMessages(needParseMessages);
            scrollToBottom();
        }
        if (errors.length > 0 && isLoading) {
            clearErrors();
        }
        if (!isLoading) {
            setMessagesa(realMessages as WeMessages);
            createMpIcon(files);
        }
    }, [realMessages, isLoading]);

    const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        if (files.length === 0) return;

        setIsUploading(true);
        try {
            const newImages = files.map(file => ({
                id: uuidv4(),
                file,
                url: URL.createObjectURL(file),
                localUrl: URL.createObjectURL(file),
                status: "done" as const,
            }));
            addImages(newImages);
        } catch (error) {
            console.error("File upload failed:", error);
            toast.error("Failed to upload files");
        } finally {
            setIsUploading(false);
        }
    };

    const handleSubmitWithFiles = async (e: any, text?: string) => {
        e?.preventDefault();
        if ((!input.trim() && !text?.trim()) || isLoading) return;

        try {
            const currentAttachments = uploadedImages.map((img) => ({
                id: img.id,
                name: img.id,
                type: img.file.type,
                localUrl: img.localUrl,
                contentType: img.file.type,
                url: img.url,
            }));

            clearImages();

            append(
                {
                    role: "user",
                    content: text || input,
                },
                {
                    experimental_attachments: currentAttachments,
                }
            );
            setInput("");
            setTimeout(() => {
                scrollToBottom();
            }, 100);
        } catch (error) {
            console.error("Upload failed:", error);
            toast.error("Failed to upload files");
        }
    };

    const handleKeySubmit = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSubmitWithFiles(e);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.files = e.dataTransfer.files;
            handleFileSelect({ target: fileInput } as any);
        }
    };

    const handleScroll = () => {
        // Handle scroll events if needed
    };

    const showJsx = useMemo(() => {
        return (
            <div
                className="flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
                onScroll={handleScroll}
            >
                <Tips
                    append={append}
                    setInput={setInput}
                    handleFileSelect={handleFileSelect}
                />
                <div className="max-w-[640px] w-full mx-auto space-y-3">
                    {filterMessages.map((message, index) => (
                        <MessageItem
                            handleRetry={() => {
                                reload();
                            }}
                            key={`${message.id}-${index}`}
                            message={message}
                            isEndMessage={
                                filterMessages[filterMessages.length - 1].id === message.id
                            }
                            isLoading={isLoading}
                            onUpdateMessage={(messageId, content) => {
                                append({
                                    role: "user",
                                    content: ` ${content?.[0]?.text}`,
                                });
                            }}
                        />
                    ))}
                    <div ref={messagesEndRef} />
                </div>
            </div>
        );
    }, [filterMessages, isLoading, append, setInput, handleFileSelect, reload]);

    return (
        <div
            className="flex h-full flex-col dark:bg-[#18181a] max-w-full"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
        >
            {showJsx}
            <ChatInput
                input={input}
                setMessages={setMessages}
                append={append}
                messages={messages}
                stopRuning={stop}
                setInput={setInput}
                isLoading={isLoading}
                isUploading={isUploading}
                uploadedImages={uploadedImages}
                baseModal={baseModal}
                handleInputChange={handleInputChange}
                handleKeySubmit={handleKeySubmit}
                handleSubmitWithFiles={handleSubmitWithFiles}
                handleFileSelect={handleFileSelect}
                removeImage={removeImage}
                addImages={addImages}
                setIsUploading={setIsUploading}
                setBaseModal={setBaseModal}
            />
        </div>
    );
};
