'use client';

import { useState, useEffect, useRef } from "react";
import { ActivityBar } from "./components/ActivityBar";
import { Terminal } from "./components/Terminal";
import { Editor } from "./components/Editor";
import { EditorTabs } from "./components/EditorTabs";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { useEditorStore } from "../../stores/editorStore";
import { FileExplorer } from "./components/IDEContent/FileExplorer";
import { Search } from "./components/IDEContent/Search";

export default function WeIde() {
  const [activeTab, setActiveTab] = useState("");
  const [showTerminal, setShowTerminal] = useState(true);
  const [openTabs, setOpenTabs] = useState<string[]>([]);
  const { setDirty } = useEditorStore();
  const [activeView, setActiveView] = useState<"files" | "search">("files");
  const [currentLine, setCurrentLine] = useState<number | undefined>();

  useEffect(() => {
    const handleEmit = (
      event: CustomEvent<{ path: string; line?: number }>
    ) => {
      handleFileSelectAiFile(event.detail.path, event.detail.line);
    };

    window.addEventListener("openFile", handleEmit as EventListener);
    return () => {
      window.removeEventListener("openFile", handleEmit as EventListener);
    };
  }, [openTabs]);

  const handleFileSelect = (path: string) => {
    if (!openTabs.includes(path)) {
      setOpenTabs([...openTabs, path]);
    }
    setActiveTab(path);
    setCurrentLine(undefined);
  };

  const handleFileSelectAiFile = (path: string, line?: number) => {
    if (!openTabs.includes(path)) {
      setOpenTabs([...openTabs, path]);
    }
    setActiveTab(path);
    setCurrentLine(line);
  };

  const handleTabClose = (tab: string) => {
    const newTabs = openTabs.filter((t) => t !== tab);
    setOpenTabs(newTabs);
    if (activeTab === tab) {
      setActiveTab(newTabs[newTabs.length - 1] || "");
    }
  };

  const handleCloseAll = () => {
    setOpenTabs([]);
    setActiveTab("");
  };

  return (
    <div
      style={{
        borderRadius: "8px",
        borderTopRightRadius: "0px",
        borderTopLeftRadius: "0px",
      }}
      className="h-full w-full bg-white dark:bg-[#18181a] text-[#333] dark:text-gray-300 flex overflow-hidden border border-[#e4e4e4] dark:border-[#333]"
    >
      {/* Activity Bar (Icon Bar) */}
      <ActivityBar
        activeView={activeView}
        onViewChange={setActiveView}
        onToggleTerminal={() => setShowTerminal(!showTerminal)}
        showTerminal={showTerminal}
      />

      <PanelGroup direction="horizontal">
        {/* File List */}
        <Panel
          defaultSize={25}
          minSize={16}
          maxSize={30}
          className="flex-shrink-0 border-r border-[#e4e4e4] dark:border-[#333]"
        >
          {activeView === "files" ? (
            <FileExplorer onFileSelect={handleFileSelect} />
          ) : (
            <Search onFileSelect={handleFileSelect} />
          )}
        </Panel>

        {/* File List Drag Handle */}
        <PanelResizeHandle className="w-[1px] bg-[#e6e6e6] hover:bg-[#e8e8e8] dark:hover:bg-[#404040] transition-colors cursor-col-resize" />
      
        {/* Coding Area and Terminal */}
        <Panel className="min-w-0 ml-[-1px]">
          <PanelGroup direction="vertical">
            {/* Coding Area */}
            <Panel className="flex flex-col min-h-0">
              <EditorTabs
                openTabs={openTabs}
                activeTab={activeTab}
                onTabSelect={setActiveTab}
                onTabClose={handleTabClose}
                onCloseAll={handleCloseAll}
              />
              <div className="flex-1 overflow-hidden bg-[#ffffff] dark:bg-[#18181a]">
                {activeTab && (
                  <Editor fileName={activeTab} initialLine={currentLine} />
                )}
              </div>
            </Panel>

            {/* Terminal */}
            {showTerminal && (
              <>
                <PanelResizeHandle className="h-[1px] bg-[#e6e6e6] hover:bg-[#e8e8e8] dark:hover:bg-[#404040] transition-colors cursor-row-resize" />
                <Panel defaultSize={30} minSize={20} maxSize={50}>
                  <Terminal />
                </Panel>
              </>
            )}
          </PanelGroup>
        </Panel>
      </PanelGroup>
    </div>
  );
}
